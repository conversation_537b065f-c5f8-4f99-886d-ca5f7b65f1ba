/// شاشة الإجازات المتقدمة
/// توفر ميزات متقدمة لإدارة الإجازات مثل التخطيط السنوي والموافقات متعددة المستويات
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/advanced_leave_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/revolutionary_design_colors.dart';

class AdvancedLeavesScreen extends StatefulWidget {
  const AdvancedLeavesScreen({super.key});

  @override
  State<AdvancedLeavesScreen> createState() => _AdvancedLeavesScreenState();
}

class _AdvancedLeavesScreenState extends State<AdvancedLeavesScreen>
    with TickerProviderStateMixin {
  final AdvancedLeaveService _advancedLeaveService = AdvancedLeaveService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<Map<String, dynamic>> _leaveTypes = [];
  List<Map<String, dynamic>> _leavePlans = [];
  List<Map<String, dynamic>> _leaveBalances = [];
  List<Map<String, dynamic>> _delegations = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;
  int? _selectedEmployeeId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // تهيئة أنواع الإجازات الافتراضية
      await _advancedLeaveService.initializeDefaultLeaveTypes();

      // تحميل البيانات
      await _loadData();
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تهيئة بيانات الإجازات المتقدمة',
        category: 'AdvancedLeavesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  Future<void> _loadData() async {
    try {
      final results = await Future.wait([
        _advancedLeaveService.getAllLeaveTypes(),
        _advancedLeaveService.getLeavePlans(),
        _advancedLeaveService.getAdvancedLeaveStatistics(),
        _employeeService.getAllEmployees(),
      ]);

      final leaveTypes = results[0] as List<Map<String, dynamic>>;
      final leavePlans = results[1] as List<Map<String, dynamic>>;
      final statistics = results[2] as Map<String, dynamic>;
      final employees = results[3] as List<Employee>;

      // تحميل رصيد الإجازات للموظف المحدد
      List<Map<String, dynamic>> leaveBalances = [];
      if (_selectedEmployeeId != null) {
        leaveBalances = await _advancedLeaveService.getEmployeeLeaveBalance(
          _selectedEmployeeId!,
          DateTime.now().year,
        );
      }

      setState(() {
        _leaveTypes = leaveTypes;
        _leavePlans = leavePlans;
        _leaveBalances = leaveBalances;
        _statistics = statistics;
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات الإجازات المتقدمة',
        category: 'AdvancedLeavesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الإجازات المتقدمة'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'init_balances':
                  _initializeAllEmployeeBalances();
                  break;
                case 'export':
                  _exportLeaveData();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'init_balances',
                child: Text('تهيئة أرصدة الإجازات'),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Text('تصدير البيانات'),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.category), text: 'أنواع الإجازات'),
            Tab(icon: Icon(Icons.account_balance_wallet), text: 'الأرصدة'),
            Tab(icon: Icon(Icons.calendar_today), text: 'التخطيط'),
            Tab(icon: Icon(Icons.swap_horiz), text: 'التفويضات'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildLeaveTypesTab(),
        _buildBalancesTab(),
        _buildPlanningTab(),
        _buildDelegationsTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات الإجازات المتقدمة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي المخصص',
              '${_statistics['totalAllocated'] ?? 0}',
              Icons.assignment,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'المستخدم',
              '${_statistics['totalUsed'] ?? 0}',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'المتبقي',
              '${_statistics['totalRemaining'] ?? 0}',
              Icons.hourglass_empty,
              RevolutionaryColors.warningAmber,
            ),
            _buildStatCard(
              'الخطط المجدولة',
              '${_statistics['totalPlanned'] ?? 0}',
              Icons.schedule,
              RevolutionaryColors.infoTurquoise,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionCard(
              'إضافة خطة إجازة',
              Icons.add_circle,
              RevolutionaryColors.successGlow,
              _showAddPlanDialog,
            ),
            _buildActionCard(
              'إضافة تفويض',
              Icons.swap_horiz,
              RevolutionaryColors.infoTurquoise,
              _showAddDelegationDialog,
            ),
            _buildActionCard(
              'عرض الأرصدة',
              Icons.account_balance_wallet,
              RevolutionaryColors.warningAmber,
              () => _tabController.animateTo(2),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: (MediaQuery.of(context).size.width - 56) / 2,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final popularTypes = _statistics['popularTypes'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'أكثر أنواع الإجازات استخداماً',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (popularTypes.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد بيانات كافية',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...popularTypes.map(
            (type) => Card(
              margin: const EdgeInsets.only(bottom: 8),
              child: ListTile(
                leading: CircleAvatar(
                  backgroundColor: RevolutionaryColors.damascusSky.withOpacity(
                    0.1,
                  ),
                  child: Text(
                    '${type['count']}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                title: Text(type['name_ar'] ?? type['leave_type']),
                subtitle: Text('${type['count']} طلب'),
              ),
            ),
          ),
      ],
    );
  }

  // دوال مساعدة ودوال الأحداث
  IconData _getLeaveTypeIcon(String leaveType) {
    switch (leaveType) {
      case 'annual':
        return Icons.beach_access;
      case 'sick':
        return Icons.local_hospital;
      case 'emergency':
        return Icons.emergency;
      case 'maternity':
        return Icons.child_care;
      case 'paternity':
        return Icons.family_restroom;
      case 'unpaid':
        return Icons.money_off;
      case 'study':
        return Icons.school;
      case 'pilgrimage':
        return Icons.mosque;
      case 'bereavement':
        return Icons.sentiment_very_dissatisfied;
      case 'marriage':
        return Icons.favorite;
      default:
        return Icons.event;
    }
  }

  Widget _buildBalanceInfo(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Color _getBalanceColor(double percentage) {
    if (percentage >= 0.8) return RevolutionaryColors.errorCoral;
    if (percentage >= 0.6) return RevolutionaryColors.warningAmber;
    return RevolutionaryColors.successGlow;
  }

  Widget _buildPlanningTab() {
    return const Center(child: Text('تبويب التخطيط - قيد التطوير'));
  }

  Widget _buildDelegationsTab() {
    return const Center(child: Text('تبويب التفويضات - قيد التطوير'));
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 3: // التخطيط
        return FloatingActionButton(
          onPressed: _showAddPlanDialog,
          backgroundColor: RevolutionaryColors.successGlow,
          child: const Icon(Icons.add, color: Colors.white),
        );
      case 4: // التفويضات
        return FloatingActionButton(
          onPressed: _showAddDelegationDialog,
          backgroundColor: RevolutionaryColors.infoTurquoise,
          child: const Icon(Icons.swap_horiz, color: Colors.white),
        );
      default:
        return null;
    }
  }

  // دوال الأحداث
  Future<void> _loadEmployeeBalance(int employeeId) async {
    try {
      final balances = await _advancedLeaveService.getEmployeeLeaveBalance(
        employeeId,
        DateTime.now().year,
      );
      setState(() {
        _leaveBalances = balances;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل أرصدة الإجازات: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _initializeEmployeeBalance(int employeeId) async {
    try {
      await _advancedLeaveService.initializeEmployeeLeaveBalance(
        employeeId,
        DateTime.now().year,
      );
      await _loadEmployeeBalance(employeeId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تهيئة أرصدة الإجازات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تهيئة الأرصدة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _initializeAllEmployeeBalances() async {
    try {
      final currentYear = DateTime.now().year;
      for (final employee in _employees) {
        await _advancedLeaveService.initializeEmployeeLeaveBalance(
          employee.id!,
          currentYear,
        );
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تهيئة أرصدة جميع الموظفين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تهيئة الأرصدة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddPlanDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة خطة إجازة - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showAddDelegationDialog() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('حوار إضافة تفويض - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _showPlanningCalendar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تقويم التخطيط - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _exportLeaveData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تصدير البيانات - قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
