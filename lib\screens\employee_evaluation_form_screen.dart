/// شاشة نموذج تقييم الموظف
/// توفر واجهة لإجراء تقييم فردي للموظف
library;

import 'package:flutter/material.dart';
import '../models/performance_evaluation_models.dart';
import '../models/hr_models.dart';
import '../services/performance_evaluation_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../constants/revolutionary_design_colors.dart';

class EmployeeEvaluationFormScreen extends StatefulWidget {
  final Employee employee;
  final EvaluationCycle cycle;
  final EmployeeEvaluation? existingEvaluation;

  const EmployeeEvaluationFormScreen({
    super.key,
    required this.employee,
    required this.cycle,
    this.existingEvaluation,
  });

  @override
  State<EmployeeEvaluationFormScreen> createState() =>
      _EmployeeEvaluationFormScreenState();
}

class _EmployeeEvaluationFormScreenState
    extends State<EmployeeEvaluationFormScreen> {
  final PerformanceEvaluationService _evaluationService =
      PerformanceEvaluationService();
  final _formKey = GlobalKey<FormState>();

  List<EvaluationCriteria> _criteria = [];
  Map<int, double> _scores = {}; // criteriaId -> score
  Map<int, String> _comments = {}; // criteriaId -> comment

  final _strengthsController = TextEditingController();
  final _weaknessesController = TextEditingController();
  final _developmentAreasController = TextEditingController();
  final _goalsController = TextEditingController();
  final _generalCommentsController = TextEditingController();

  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;
  double _overallScore = 0.0;
  String _overallRating = 'satisfactory';

  @override
  void initState() {
    super.initState();
    _loadData();
    _initializeExistingData();
  }

  @override
  void dispose() {
    _strengthsController.dispose();
    _weaknessesController.dispose();
    _developmentAreasController.dispose();
    _goalsController.dispose();
    _generalCommentsController.dispose();
    super.dispose();
  }

  void _initializeExistingData() {
    if (widget.existingEvaluation != null) {
      final evaluation = widget.existingEvaluation!;
      _strengthsController.text = evaluation.strengths ?? '';
      _weaknessesController.text = evaluation.weaknesses ?? '';
      _developmentAreasController.text = evaluation.developmentAreas ?? '';
      _goalsController.text = evaluation.goals ?? '';
      _generalCommentsController.text = evaluation.comments ?? '';
      _overallScore = evaluation.overallScore ?? 0.0;
      _overallRating = evaluation.overallRating ?? 'satisfactory';
    }
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final criteria = await _evaluationService.getAllEvaluationCriteria(
        isActive: true,
      );

      // تحميل تفاصيل التقييم الموجود إن وجد
      if (widget.existingEvaluation != null) {
        final details = await _evaluationService.getEvaluationDetails(
          widget.existingEvaluation!.id!,
        );
        for (final detail in details) {
          _scores[detail.criteriaId] = detail.score;
          _comments[detail.criteriaId] = detail.comments ?? '';
        }
      }

      setState(() {
        _criteria = criteria;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات التقييم',
        category: 'EmployeeEvaluationFormScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'تقييم ${widget.employee.firstName} ${widget.employee.lastName}',
        ),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _error == null)
            TextButton(
              onPressed: _isSaving ? null : _saveDraft,
              child: const Text(
                'حفظ مسودة',
                style: TextStyle(color: Colors.white),
              ),
            ),
        ],
      ),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'خطأ في تحميل البيانات',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildEmployeeInfo(),
            const SizedBox(height: 24),
            _buildCycleInfo(),
            const SizedBox(height: 24),
            _buildCriteriaEvaluation(),
            const SizedBox(height: 24),
            _buildOverallEvaluation(),
            const SizedBox(height: 24),
            _buildQualitativeEvaluation(),
            const SizedBox(height: 100), // مساحة للشريط السفلي
          ],
        ),
      ),
    );
  }

  Widget _buildEmployeeInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الموظف',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: RevolutionaryColors.damascusSky.withOpacity(
                    0.1,
                  ),
                  child: Text(
                    widget.employee.firstName.isNotEmpty
                        ? widget.employee.firstName[0]
                        : '؟',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: RevolutionaryColors.damascusSky,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.employee.firstName} ${widget.employee.lastName}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الموظف: ${widget.employee.employeeNumber}',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      Text(
                        'الراتب الأساسي: ${widget.employee.basicSalary.toStringAsFixed(0)} ل.س',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات دورة التقييم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('اسم الدورة', widget.cycle.name),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'النوع',
                    _getEvaluationTypeText(widget.cycle.evaluationType),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ البداية',
                    '${widget.cycle.startDate.day}/${widget.cycle.startDate.month}/${widget.cycle.startDate.year}',
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ النهاية',
                    '${widget.cycle.endDate.day}/${widget.cycle.endDate.month}/${widget.cycle.endDate.year}',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildCriteriaEvaluation() {
    if (_criteria.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.rule, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد معايير تقييم',
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'يرجى إضافة معايير التقييم أولاً',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معايير التقييم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ..._criteria.map((criteria) => _buildCriteriaItem(criteria)),
          ],
        ),
      ),
    );
  }

  Widget _buildCriteriaItem(EvaluationCriteria criteria) {
    final score = _scores[criteria.id] ?? 0.0;
    final comment = _comments[criteria.id] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      criteria.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      criteria.description,
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.infoTurquoise.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  criteria.category,
                  style: const TextStyle(
                    fontSize: 12,
                    color: RevolutionaryColors.infoTurquoise,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'التقييم: ${score.toStringAsFixed(1)} / ${criteria.maxScore}',
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          Slider(
            value: score,
            min: 0,
            max: criteria.maxScore.toDouble(),
            divisions: criteria.maxScore,
            activeColor: RevolutionaryColors.successGlow,
            onChanged: (value) {
              setState(() {
                _scores[criteria.id!] = value;
                _calculateOverallScore();
              });
            },
          ),
          const SizedBox(height: 12),
          TextFormField(
            initialValue: comment,
            decoration: const InputDecoration(
              labelText: 'تعليقات',
              hintText: 'أضف تعليقاتك حول هذا المعيار...',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
            onChanged: (value) {
              _comments[criteria.id!] = value;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildOverallEvaluation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقييم الإجمالي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: RevolutionaryColors.successGlow.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: RevolutionaryColors.successGlow.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.star,
                    size: 32,
                    color: RevolutionaryColors.successGlow,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'النتيجة الإجمالية',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                      Text(
                        '${_overallScore.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: RevolutionaryColors.successGlow,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  DropdownButton<String>(
                    value: _overallRating,
                    items: const [
                      DropdownMenuItem(
                        value: 'excellent',
                        child: Text('ممتاز'),
                      ),
                      DropdownMenuItem(value: 'good', child: Text('جيد')),
                      DropdownMenuItem(
                        value: 'satisfactory',
                        child: Text('مرضي'),
                      ),
                      DropdownMenuItem(
                        value: 'needs_improvement',
                        child: Text('يحتاج تحسين'),
                      ),
                      DropdownMenuItem(
                        value: 'unsatisfactory',
                        child: Text('غير مرضي'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _overallRating = value;
                        });
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQualitativeEvaluation() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقييم النوعي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _strengthsController,
              decoration: const InputDecoration(
                labelText: 'نقاط القوة',
                hintText: 'اذكر نقاط القوة الرئيسية للموظف...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.thumb_up,
                  color: RevolutionaryColors.successGlow,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _weaknessesController,
              decoration: const InputDecoration(
                labelText: 'نقاط الضعف',
                hintText: 'اذكر المجالات التي تحتاج إلى تحسين...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.thumb_down,
                  color: RevolutionaryColors.warningAmber,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _developmentAreasController,
              decoration: const InputDecoration(
                labelText: 'مجالات التطوير',
                hintText: 'اقترح مجالات للتطوير والتحسين...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.trending_up,
                  color: RevolutionaryColors.infoTurquoise,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _goalsController,
              decoration: const InputDecoration(
                labelText: 'الأهداف المستقبلية',
                hintText: 'حدد الأهداف للفترة القادمة...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(
                  Icons.flag,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _generalCommentsController,
              decoration: const InputDecoration(
                labelText: 'تعليقات عامة',
                hintText: 'أضف أي تعليقات إضافية...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.comment, color: Colors.grey),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isSaving ? null : _saveDraft,
              child: _isSaving
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('حفظ مسودة'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton(
              onPressed: _isSaving ? null : _submitEvaluation,
              style: ElevatedButton.styleFrom(
                backgroundColor: RevolutionaryColors.successGlow,
                foregroundColor: Colors.white,
              ),
              child: _isSaving
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('إرسال التقييم'),
            ),
          ),
        ],
      ),
    );
  }

  // دوال مساعدة
  String _getEvaluationTypeText(String type) {
    switch (type) {
      case 'annual':
        return 'سنوي';
      case 'quarterly':
        return 'ربع سنوي';
      case 'monthly':
        return 'شهري';
      case 'project_based':
        return 'حسب المشروع';
      default:
        return type;
    }
  }

  void _calculateOverallScore() {
    if (_criteria.isEmpty) {
      _overallScore = 0.0;
      return;
    }

    double totalWeightedScore = 0.0;
    double totalWeight = 0.0;

    for (final criteria in _criteria) {
      final score = _scores[criteria.id] ?? 0.0;
      final normalizedScore = (score / criteria.maxScore) * 100;
      totalWeightedScore += normalizedScore * criteria.weight;
      totalWeight += criteria.weight;
    }

    _overallScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0.0;
  }

  Future<void> _saveDraft() async {
    await _saveEvaluation('draft');
  }

  Future<void> _submitEvaluation() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود تقييمات لجميع المعايير
    for (final criteria in _criteria) {
      if (!_scores.containsKey(criteria.id) || _scores[criteria.id] == 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('يرجى تقييم معيار: ${criteria.name}'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }
    }

    await _saveEvaluation('submitted');
  }

  Future<void> _saveEvaluation(String status) async {
    setState(() {
      _isSaving = true;
    });

    try {
      EmployeeEvaluation evaluation;

      if (widget.existingEvaluation != null) {
        // تحديث التقييم الموجود
        evaluation = await _evaluationService.updateEvaluationStatus(
          evaluationId: widget.existingEvaluation!.id!,
          status: status,
          overallScore: _overallScore,
          overallRating: _overallRating,
          strengths: _strengthsController.text.trim().isEmpty
              ? null
              : _strengthsController.text.trim(),
          weaknesses: _weaknessesController.text.trim().isEmpty
              ? null
              : _weaknessesController.text.trim(),
          developmentAreas: _developmentAreasController.text.trim().isEmpty
              ? null
              : _developmentAreasController.text.trim(),
          goals: _goalsController.text.trim().isEmpty
              ? null
              : _goalsController.text.trim(),
          comments: _generalCommentsController.text.trim().isEmpty
              ? null
              : _generalCommentsController.text.trim(),
        );
      } else {
        // إنشاء تقييم جديد
        evaluation = await _evaluationService.createEmployeeEvaluation(
          employeeId: widget.employee.id!,
          cycleId: widget.cycle.id!,
        );

        // تحديث التقييم بالبيانات
        evaluation = await _evaluationService.updateEvaluationStatus(
          evaluationId: evaluation.id!,
          status: status,
          overallScore: _overallScore,
          overallRating: _overallRating,
          strengths: _strengthsController.text.trim().isEmpty
              ? null
              : _strengthsController.text.trim(),
          weaknesses: _weaknessesController.text.trim().isEmpty
              ? null
              : _weaknessesController.text.trim(),
          developmentAreas: _developmentAreasController.text.trim().isEmpty
              ? null
              : _developmentAreasController.text.trim(),
          goals: _goalsController.text.trim().isEmpty
              ? null
              : _goalsController.text.trim(),
          comments: _generalCommentsController.text.trim().isEmpty
              ? null
              : _generalCommentsController.text.trim(),
        );
      }

      // حفظ تفاصيل التقييم
      for (final criteria in _criteria) {
        if (_scores.containsKey(criteria.id)) {
          final detail = EvaluationDetail(
            evaluationId: evaluation.id!,
            criteriaId: criteria.id!,
            score: _scores[criteria.id]!,
            comments: _comments[criteria.id]?.trim().isEmpty == true
                ? null
                : _comments[criteria.id]?.trim(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );

          await _evaluationService.addEvaluationDetail(detail);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              status == 'draft'
                  ? 'تم حفظ المسودة بنجاح'
                  : 'تم إرسال التقييم بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );

        if (status == 'submitted') {
          Navigator.of(
            context,
          ).pop(true); // إرجاع true للإشارة إلى نجاح الإرسال
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التقييم: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}
