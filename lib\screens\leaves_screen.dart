/// شاشة إدارة الإجازات
/// توفر واجهة لإدارة طلبات الإجازات ونظام الموافقات
library;

import 'package:flutter/material.dart';
import '../models/hr_models.dart';
import '../services/leave_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../services/auth_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;
import '../constants/app_constants.dart';

class LeavesScreen extends StatefulWidget {
  const LeavesScreen({super.key});

  @override
  State<LeavesScreen> createState() => _LeavesScreenState();
}

class _LeavesScreenState extends State<LeavesScreen>
    with TickerProviderStateMixin {
  final LeaveService _leaveService = LeaveService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<Leave> _allLeaves = [];
  List<Leave> _pendingLeaves = [];
  List<Employee> _employees = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final allLeaves = await _leaveService.getAllLeaves();
      final pendingLeaves = await _leaveService.getPendingLeaves();
      final employees = await _employeeService.getAllEmployees();

      setState(() {
        _allLeaves = allLeaves;
        _pendingLeaves = pendingLeaves;
        _employees = employees;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل الإجازات',
        category: 'LeavesScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الإجازات'),
        backgroundColor: Colors.orange[700],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              icon: const Icon(Icons.list),
              text: 'جميع الإجازات',
              child: _pendingLeaves.isNotEmpty
                  ? Badge(
                      label: Text('${_pendingLeaves.length}'),
                      child: const Text('جميع الإجازات'),
                    )
                  : const Text('جميع الإجازات'),
            ),
            Tab(
              icon: const Icon(Icons.pending_actions),
              text: 'المعلقة',
              child: _pendingLeaves.isNotEmpty
                  ? Badge(
                      label: Text('${_pendingLeaves.length}'),
                      child: const Text('المعلقة'),
                    )
                  : const Text('المعلقة'),
            ),
            const Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddLeaveDialog(),
        backgroundColor: Colors.orange[700],
        tooltip: 'طلب إجازة جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildAllLeavesTab(),
        _buildPendingLeavesTab(),
        _buildStatisticsTab(),
      ],
    );
  }

  Widget _buildAllLeavesTab() {
    if (_allLeaves.isEmpty) {
      return _buildEmptyState('لا توجد طلبات إجازات');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _allLeaves.length,
        itemBuilder: (context, index) {
          final leave = _allLeaves[index];
          return _buildLeaveCard(leave);
        },
      ),
    );
  }

  Widget _buildPendingLeavesTab() {
    if (_pendingLeaves.isEmpty) {
      return _buildEmptyState('لا توجد طلبات إجازات معلقة');
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _pendingLeaves.length,
        itemBuilder: (context, index) {
          final leave = _pendingLeaves[index];
          return _buildLeaveCard(leave, showActions: true);
        },
      ),
    );
  }

  Widget _buildStatisticsTab() {
    final totalLeaves = _allLeaves.length;
    final pendingCount = _pendingLeaves.length;
    final approvedCount = _allLeaves.where((l) => l.isApproved).length;
    final rejectedCount = _allLeaves.where((l) => l.isRejected).length;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildStatCard('إجمالي الطلبات', totalLeaves, Colors.blue),
          const SizedBox(height: 12),
          _buildStatCard('المعلقة', pendingCount, Colors.orange),
          const SizedBox(height: 12),
          _buildStatCard('الموافق عليها', approvedCount, Colors.green),
          const SizedBox(height: 12),
          _buildStatCard('المرفوضة', rejectedCount, Colors.red),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, int count, Color color) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color,
          child: Text(
            count.toString(),
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(title),
        subtitle: Text('$count طلب'),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.event_busy, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(fontSize: 18, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: () => _showAddLeaveDialog(),
            icon: const Icon(Icons.add),
            label: const Text('طلب إجازة جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[700],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaveCard(Leave leave, {bool showActions = false}) {
    final employee = _employees.firstWhere(
      (emp) => emp.id == leave.employeeId,
      orElse: () => Employee(
        id: 0,
        employeeNumber: '000',
        nationalId: '000000000',
        firstName: 'موظف',
        lastName: 'غير معروف',
        fullName: 'موظف غير معروف',
        email: '',
        phone: '',
        hireDate: DateTime.now(),
        basicSalary: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Color statusColor;
    IconData statusIcon;
    String statusText;

    if (leave.isPending) {
      statusColor = Colors.orange;
      statusIcon = Icons.pending;
      statusText = 'معلق';
    } else if (leave.isApproved) {
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
      statusText = 'موافق';
    } else if (leave.isRejected) {
      statusColor = Colors.red;
      statusIcon = Icons.cancel;
      statusText = 'مرفوض';
    } else {
      statusColor = Colors.grey;
      statusIcon = Icons.help;
      statusText = 'غير معروف';
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${employee.firstName} ${employee.lastName}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(color: statusColor, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('نوع الإجازة: ${_getLeaveTypeText(leave.leaveType)}'),
            Text(
              'من: ${_formatDate(leave.startDate)} إلى: ${_formatDate(leave.endDate)}',
            ),
            Text('عدد الأيام: ${leave.totalDays}'),
            if (leave.reason != null && leave.reason!.isNotEmpty)
              Text('السبب: ${leave.reason}'),
            if (leave.rejectionReason != null &&
                leave.rejectionReason!.isNotEmpty)
              Text(
                'سبب الرفض: ${leave.rejectionReason}',
                style: const TextStyle(color: Colors.red),
              ),
            if (showActions && leave.isPending) ...[
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton.icon(
                    onPressed: () => _rejectLeave(leave),
                    icon: const Icon(Icons.close, color: Colors.red),
                    label: const Text(
                      'رفض',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _approveLeave(leave),
                    icon: const Icon(Icons.check, color: Colors.white),
                    label: const Text(
                      'موافقة',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getLeaveTypeText(String leaveType) {
    switch (leaveType) {
      case AppConstants.leaveTypeAnnual:
        return 'إجازة سنوية';
      case AppConstants.leaveTypeSick:
        return 'إجازة مرضية';
      case AppConstants.leaveTypeEmergency:
        return 'إجازة طارئة';
      case AppConstants.leaveTypeMaternity:
        return 'إجازة أمومة';
      case AppConstants.leaveTypePaternity:
        return 'إجازة أبوة';
      default:
        return leaveType;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showAddLeaveDialog() {
    if (_employees.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد موظفين لطلب إجازة لهم'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _LeaveFormDialog(
        employees: _employees,
        onSave: (employeeId, leaveType, startDate, endDate, reason) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            await _leaveService.createLeaveRequest(
              employeeId: employeeId,
              leaveType: leaveType,
              startDate: startDate,
              endDate: endDate,
              reason: reason.isEmpty ? null : reason,
            );

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم إنشاء طلب الإجازة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
              _loadData();
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في إنشاء طلب الإجازة: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _approveLeave(Leave leave) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      // التحقق من وجود مستخدم مسجل دخول
      final currentUser = AuthService.currentUser;
      if (currentUser?.id == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      await _leaveService.approveLeave(
        leaveId: leave.id!,
        approvedBy: currentUser!.id!,
      );

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('تم الموافقة على طلب الإجازة'),
            backgroundColor: Colors.green,
          ),
        );
        _loadData();
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('خطأ في الموافقة على الطلب: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _rejectLeave(Leave leave) {
    showDialog(
      context: context,
      builder: (context) => _RejectLeaveDialog(
        onReject: (reason) async {
          final scaffoldMessenger = ScaffoldMessenger.of(context);
          try {
            await _leaveService.rejectLeave(
              leaveId: leave.id!,
              rejectedBy: 1, // TODO: استخدام معرف المستخدم الحالي
              rejectionReason: reason,
            );

            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('تم رفض طلب الإجازة'),
                  backgroundColor: Colors.orange,
                ),
              );
              _loadData();
            }
          } catch (e) {
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                SnackBar(
                  content: Text('خطأ في رفض الطلب: ${e.toString()}'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }
}

class _LeaveFormDialog extends StatefulWidget {
  final List<Employee> employees;
  final Function(
    int employeeId,
    String leaveType,
    DateTime startDate,
    DateTime endDate,
    String reason,
  )
  onSave;

  const _LeaveFormDialog({required this.employees, required this.onSave});

  @override
  State<_LeaveFormDialog> createState() => _LeaveFormDialogState();
}

class _LeaveFormDialogState extends State<_LeaveFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  int? _selectedEmployeeId;
  String _selectedLeaveType = AppConstants.leaveTypeAnnual;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('طلب إجازة جديد'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<int>(
                value: _selectedEmployeeId,
                decoration: const InputDecoration(
                  labelText: 'الموظف *',
                  border: OutlineInputBorder(),
                ),
                items: widget.employees.map((emp) {
                  return DropdownMenuItem<int>(
                    value: emp.id,
                    child: Text('${emp.firstName} ${emp.lastName}'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedEmployeeId = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return 'الموظف مطلوب';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedLeaveType,
                decoration: const InputDecoration(
                  labelText: 'نوع الإجازة *',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: AppConstants.leaveTypeAnnual,
                    child: Text('إجازة سنوية'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.leaveTypeSick,
                    child: Text('إجازة مرضية'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.leaveTypeEmergency,
                    child: Text('إجازة طارئة'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.leaveTypeMaternity,
                    child: Text('إجازة أمومة'),
                  ),
                  DropdownMenuItem(
                    value: AppConstants.leaveTypePaternity,
                    child: Text('إجازة أبوة'),
                  ),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedLeaveType = value!;
                  });
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ البداية *',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() {
                            _startDate = date;
                          });
                        }
                      },
                      controller: TextEditingController(
                        text: _startDate != null
                            ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                            : '',
                      ),
                      validator: (value) {
                        if (_startDate == null) {
                          return 'تاريخ البداية مطلوب';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تاريخ النهاية *',
                        border: OutlineInputBorder(),
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      readOnly: true,
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _startDate ?? DateTime.now(),
                          firstDate: _startDate ?? DateTime.now(),
                          lastDate: DateTime.now().add(
                            const Duration(days: 365),
                          ),
                        );
                        if (date != null) {
                          setState(() {
                            _endDate = date;
                          });
                        }
                      },
                      controller: TextEditingController(
                        text: _endDate != null
                            ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                            : '',
                      ),
                      validator: (value) {
                        if (_endDate == null) {
                          return 'تاريخ النهاية مطلوب';
                        }
                        if (_startDate != null &&
                            _endDate!.isBefore(_startDate!)) {
                          return 'يجب أن يكون بعد تاريخ البداية';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _reasonController,
                decoration: const InputDecoration(
                  labelText: 'السبب',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave(
                _selectedEmployeeId!,
                _selectedLeaveType,
                _startDate!,
                _endDate!,
                _reasonController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange[700],
            foregroundColor: Colors.white,
          ),
          child: const Text('إنشاء الطلب'),
        ),
      ],
    );
  }
}

class _RejectLeaveDialog extends StatefulWidget {
  final Function(String reason) onReject;

  const _RejectLeaveDialog({required this.onReject});

  @override
  State<_RejectLeaveDialog> createState() => _RejectLeaveDialogState();
}

class _RejectLeaveDialogState extends State<_RejectLeaveDialog> {
  final _reasonController = TextEditingController();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('رفض طلب الإجازة'),
      content: TextFormField(
        controller: _reasonController,
        decoration: const InputDecoration(
          labelText: 'سبب الرفض *',
          border: OutlineInputBorder(),
        ),
        maxLines: 3,
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'سبب الرفض مطلوب';
          }
          return null;
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_reasonController.text.trim().isNotEmpty) {
              widget.onReject(_reasonController.text.trim());
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          child: const Text('رفض', style: TextStyle(color: Colors.white)),
        ),
      ],
    );
  }
}
