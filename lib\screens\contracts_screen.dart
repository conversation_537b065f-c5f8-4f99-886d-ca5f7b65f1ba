/// شاشة إدارة العقود
/// واجهة مبسطة لإدارة عقود الموظفين
library;

import 'package:flutter/material.dart';
import '../constants/revolutionary_design_colors.dart';
import '../models/hr_models.dart';
import '../services/advanced_contract_service.dart';
import '../services/employee_service.dart';
import '../services/logging_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/custom_error_widget.dart' as custom_widgets;

class ContractsScreen extends StatefulWidget {
  const ContractsScreen({super.key});

  @override
  State<ContractsScreen> createState() => _ContractsScreenState();
}

class _ContractsScreenState extends State<ContractsScreen>
    with SingleTickerProviderStateMixin {
  final AdvancedContractService _contractService = AdvancedContractService();
  final EmployeeService _employeeService = EmployeeService();

  late TabController _tabController;
  List<EmployeeContract> _allContracts = [];
  List<EmployeeContract> _activeContracts = [];
  List<EmployeeContract> _expiringSoonContracts = [];
  List<Employee> _employees = [];
  Map<String, dynamic> _statistics = {};

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _contractService.getAllContracts(),
        _contractService.getActiveContracts(),
        _contractService.getExpiringSoonContracts(),
        _contractService.getContractsStatistics(),
        _employeeService.getAllEmployees(),
      ]);

      setState(() {
        _allContracts = results[0] as List<EmployeeContract>;
        _activeContracts = results[1] as List<EmployeeContract>;
        _expiringSoonContracts = results[2] as List<EmployeeContract>;
        _statistics = results[3] as Map<String, dynamic>;
        _employees = results[4] as List<Employee>;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });

      LoggingService.error(
        'خطأ في تحميل بيانات العقود',
        category: 'ContractsScreen',
        data: {'error': e.toString()},
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة العقود'),
        backgroundColor: RevolutionaryColors.infoTurquoise,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddContractDialog,
            tooltip: 'إضافة عقد جديد',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
            Tab(icon: Icon(Icons.description), text: 'جميع العقود'),
            Tab(icon: Icon(Icons.check_circle), text: 'العقود النشطة'),
            Tab(icon: Icon(Icons.warning), text: 'تنتهي قريباً'),
          ],
        ),
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddContractDialog,
        backgroundColor: RevolutionaryColors.infoTurquoise,
        tooltip: 'إضافة عقد جديد',
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const LoadingWidget();
    }

    if (_error != null) {
      return custom_widgets.ErrorWidget(message: _error!, onRetry: _loadData);
    }

    return TabBarView(
      controller: _tabController,
      children: [
        _buildOverviewTab(),
        _buildAllContractsTab(),
        _buildActiveContractsTab(),
        _buildExpiringSoonTab(),
      ],
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatisticsCards(),
          const SizedBox(height: 24),
          _buildQuickActions(),
          const SizedBox(height: 24),
          _buildRecentContracts(),
        ],
      ),
    );
  }

  Widget _buildStatisticsCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'إحصائيات العقود',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: [
            _buildStatCard(
              'إجمالي العقود',
              '${_statistics['totalContracts'] ?? 0}',
              Icons.description,
              RevolutionaryColors.damascusSky,
            ),
            _buildStatCard(
              'العقود النشطة',
              '${_statistics['activeContracts'] ?? 0}',
              Icons.check_circle,
              RevolutionaryColors.successGlow,
            ),
            _buildStatCard(
              'تنتهي قريباً',
              '${_statistics['expiringSoonContracts'] ?? 0}',
              Icons.warning,
              RevolutionaryColors.warningAmber,
            ),
            _buildStatCard(
              'العقود المنتهية',
              '${_statistics['expiredContracts'] ?? 0}',
              Icons.cancel,
              RevolutionaryColors.errorCoral,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'إضافة عقد جديد',
                Icons.add,
                RevolutionaryColors.successGlow,
                _showAddContractDialog,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'فحص العقود المنتهية',
                Icons.search,
                RevolutionaryColors.warningAmber,
                _checkExpiringContracts,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(fontWeight: FontWeight.w600, color: color),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentContracts() {
    final recentContracts = _allContracts.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'العقود الحديثة',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (recentContracts.isEmpty)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: Text(
                'لا توجد عقود حتى الآن',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          )
        else
          ...recentContracts.map((contract) => _buildContractCard(contract)),
      ],
    );
  }

  Widget _buildAllContractsTab() {
    return _buildContractsList(_allContracts, 'لا توجد عقود');
  }

  Widget _buildActiveContractsTab() {
    return _buildContractsList(_activeContracts, 'لا توجد عقود نشطة');
  }

  Widget _buildExpiringSoonTab() {
    return _buildContractsList(
      _expiringSoonContracts,
      'لا توجد عقود تنتهي قريباً',
    );
  }

  Widget _buildContractsList(
    List<EmployeeContract> contracts,
    String emptyMessage,
  ) {
    if (contracts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: contracts.length,
      itemBuilder: (context, index) {
        return _buildContractCard(contracts[index]);
      },
    );
  }

  Widget _buildContractCard(EmployeeContract contract) {
    final employee = _employees.firstWhere(
      (emp) => emp.id == contract.employeeId,
      orElse: () => Employee(
        id: 0,
        employeeNumber: '000',
        nationalId: '000000000',
        firstName: 'موظف',
        lastName: 'غير معروف',
        fullName: 'موظف غير معروف',
        email: '',
        phone: '',
        hireDate: DateTime.now(),
        basicSalary: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (contract.isExpired) {
      statusColor = RevolutionaryColors.errorCoral;
      statusText = 'منتهي';
      statusIcon = Icons.cancel;
    } else if (contract.isExpiringSoon) {
      statusColor = RevolutionaryColors.warningAmber;
      statusText = 'ينتهي قريباً';
      statusIcon = Icons.warning;
    } else if (contract.isActive) {
      statusColor = RevolutionaryColors.successGlow;
      statusText = 'نشط';
      statusIcon = Icons.check_circle;
    } else {
      statusColor = Colors.grey;
      statusText = 'غير نشط';
      statusIcon = Icons.pause_circle;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        employee.fullName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الموظف: ${employee.employeeNumber}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: statusColor.withOpacity(0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 16, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        statusText,
                        style: TextStyle(
                          fontSize: 12,
                          color: statusColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'نوع العقد',
                    _getContractTypeText(contract.contractType),
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'الراتب',
                    '${contract.salary.toStringAsFixed(0)} ل.س',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ البداية',
                    _formatDate(contract.startDate),
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'تاريخ النهاية',
                    contract.endDate != null
                        ? _formatDate(contract.endDate!)
                        : 'مفتوح',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  String _getContractTypeText(String contractType) {
    switch (contractType) {
      case 'permanent':
        return 'دائم';
      case 'temporary':
        return 'مؤقت';
      case 'part_time':
        return 'دوام جزئي';
      case 'full_time':
        return 'دوام كامل';
      case 'contract':
        return 'تعاقد';
      default:
        return contractType;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showAddContractDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddContractDialog(
        employees: _employees,
        onSave:
            (
              employeeId,
              contractType,
              startDate,
              endDate,
              salary,
              workingHours,
              benefits,
              terms,
              notes,
            ) async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              try {
                final contract = EmployeeContract(
                  employeeId: employeeId,
                  contractType: contractType,
                  startDate: startDate,
                  endDate: endDate,
                  salary: salary,
                  workingHours: workingHours,
                  benefits: benefits,
                  terms: terms,
                  notes: notes,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                );

                await _contractService.addContract(contract);

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم إضافة العقد بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadData();
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('خطأ في إضافة العقد: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
      ),
    );
  }

  void _checkExpiringContracts() async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      await _contractService.checkExpiringContracts();
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('تم فحص العقود المنتهية وإنشاء التنبيهات'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('خطأ في فحص العقود: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class _AddContractDialog extends StatefulWidget {
  final List<Employee> employees;
  final Function(
    int employeeId,
    String contractType,
    DateTime startDate,
    DateTime? endDate,
    double salary,
    String workingHours,
    String? benefits,
    String? terms,
    String? notes,
  )
  onSave;

  const _AddContractDialog({required this.employees, required this.onSave});

  @override
  State<_AddContractDialog> createState() => _AddContractDialogState();
}

class _AddContractDialogState extends State<_AddContractDialog> {
  final _formKey = GlobalKey<FormState>();
  final _salaryController = TextEditingController();
  final _workingHoursController = TextEditingController(text: '8 ساعات');
  final _benefitsController = TextEditingController();
  final _termsController = TextEditingController();
  final _notesController = TextEditingController();

  int? _selectedEmployeeId;
  String _selectedContractType = 'permanent';
  DateTime? _startDate;
  DateTime? _endDate;

  final List<Map<String, String>> _contractTypes = [
    {'value': 'permanent', 'label': 'دائم'},
    {'value': 'temporary', 'label': 'مؤقت'},
    {'value': 'part_time', 'label': 'دوام جزئي'},
    {'value': 'full_time', 'label': 'دوام كامل'},
    {'value': 'contract', 'label': 'تعاقد'},
  ];

  @override
  void dispose() {
    _salaryController.dispose();
    _workingHoursController.dispose();
    _benefitsController.dispose();
    _termsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة عقد جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اختيار الموظف
                DropdownButtonFormField<int>(
                  value: _selectedEmployeeId,
                  decoration: const InputDecoration(
                    labelText: 'الموظف',
                    border: OutlineInputBorder(),
                  ),
                  items: widget.employees.map((employee) {
                    return DropdownMenuItem<int>(
                      value: employee.id,
                      child: Text(
                        '${employee.fullName} (${employee.employeeNumber})',
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedEmployeeId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الموظف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // نوع العقد
                DropdownButtonFormField<String>(
                  value: _selectedContractType,
                  decoration: const InputDecoration(
                    labelText: 'نوع العقد',
                    border: OutlineInputBorder(),
                  ),
                  items: _contractTypes.map((type) {
                    return DropdownMenuItem<String>(
                      value: type['value'],
                      child: Text(type['label']!),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedContractType = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // تاريخ البداية
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ بداية العقد',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime(2020),
                      lastDate: DateTime(2030),
                    );
                    if (date != null) {
                      setState(() {
                        _startDate = date;
                      });
                    }
                  },
                  controller: TextEditingController(
                    text: _startDate != null
                        ? '${_startDate!.day}/${_startDate!.month}/${_startDate!.year}'
                        : '',
                  ),
                  validator: (value) {
                    if (_startDate == null) {
                      return 'يرجى اختيار تاريخ البداية';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // تاريخ النهاية (اختياري)
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'تاريخ نهاية العقد (اختياري)',
                    border: OutlineInputBorder(),
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  readOnly: true,
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _startDate ?? DateTime.now(),
                      firstDate: _startDate ?? DateTime(2020),
                      lastDate: DateTime(2030),
                    );
                    if (date != null) {
                      setState(() {
                        _endDate = date;
                      });
                    }
                  },
                  controller: TextEditingController(
                    text: _endDate != null
                        ? '${_endDate!.day}/${_endDate!.month}/${_endDate!.year}'
                        : '',
                  ),
                ),
                const SizedBox(height: 16),

                // الراتب
                TextFormField(
                  controller: _salaryController,
                  decoration: const InputDecoration(
                    labelText: 'الراتب الأساسي',
                    border: OutlineInputBorder(),
                    suffixText: 'ل.س',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الراتب';
                    }
                    if (double.tryParse(value) == null) {
                      return 'يرجى إدخال رقم صحيح';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // ساعات العمل
                TextFormField(
                  controller: _workingHoursController,
                  decoration: const InputDecoration(
                    labelText: 'ساعات العمل',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),

                // المزايا
                TextFormField(
                  controller: _benefitsController,
                  decoration: const InputDecoration(
                    labelText: 'المزايا (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 16),

                // الشروط
                TextFormField(
                  controller: _termsController,
                  decoration: const InputDecoration(
                    labelText: 'شروط العقد (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // ملاحظات
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              widget.onSave(
                _selectedEmployeeId!,
                _selectedContractType,
                _startDate!,
                _endDate,
                double.parse(_salaryController.text),
                _workingHoursController.text.trim(),
                _benefitsController.text.trim().isEmpty
                    ? null
                    : _benefitsController.text.trim(),
                _termsController.text.trim().isEmpty
                    ? null
                    : _termsController.text.trim(),
                _notesController.text.trim().isEmpty
                    ? null
                    : _notesController.text.trim(),
              );
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.infoTurquoise,
            foregroundColor: Colors.white,
          ),
          child: const Text('إضافة العقد'),
        ),
      ],
    );
  }
}
